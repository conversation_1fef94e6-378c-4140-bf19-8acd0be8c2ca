plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

allprojects {
    group = 'com.nttdata.ndvn.order'
    version = '1.0.0-SNAPSHOT'
    
    repositories {
        mavenCentral()
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'io.spring.dependency-management'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    
    dependencyManagement {
        imports {
            mavenBom 'org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE'
            mavenBom 'org.springframework.boot:spring-boot-dependencies:3.4.1'
            mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2024.0.0'
        }
    }
    
    dependencies {
        // Core Spring dependencies
        implementation 'org.springframework:spring-context'
        implementation 'org.springframework:spring-core'
        implementation 'jakarta.annotation:jakarta.annotation-api'
        
        // Logging
        implementation 'org.slf4j:slf4j-api'
        implementation 'ch.qos.logback:logback-classic'
        implementation 'net.logstash.logback:logstash-logback-encoder:7.4'
        
        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testImplementation 'org.mockito:mockito-core'
        testImplementation 'org.mockito:mockito-junit-jupiter'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    }
    
    test {
        useJUnitPlatform()
    }
    
    // Common configuration for all modules
    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs += ['-parameters']
    }
    
    compileTestJava {
        options.encoding = 'UTF-8'
    }
}

// Configure specific modules
configure(project(':order-domain')) {
    dependencies {
        // Domain layer should have minimal dependencies
        implementation 'org.terasoluna.gfw:terasoluna-gfw-common'
        implementation 'jakarta.persistence:jakarta.persistence-api'
        implementation 'jakarta.validation:jakarta.validation-api'
        implementation 'org.springframework.security:spring-security-core'
    }
}

configure(project(':order-infrastructure')) {
    dependencies {
        implementation project(':order-domain')

        // Shared Infrastructure Framework
        implementation project(':shared-infrastructure')

        // Data access
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.postgresql:postgresql'
        implementation 'org.flywaydb:flyway-core'
        
        // Security
        implementation 'org.springframework.security:spring-security-crypto'
        
        // Caching
        implementation 'org.springframework.boot:spring-boot-starter-cache'
        implementation 'org.springframework.boot:spring-boot-starter-data-redis'
        
        // External service clients
        implementation 'org.springframework.boot:spring-boot-starter-webflux'
        implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
        
        // Testing
        testImplementation 'org.testcontainers:junit-jupiter'
        testImplementation 'org.testcontainers:postgresql'
        testImplementation 'com.h2database:h2'
    }
}

configure(project(':order-application')) {
    dependencies {
        implementation project(':order-domain')
        implementation project(':order-infrastructure')

        // Application services
        implementation 'org.springframework:spring-tx'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.data:spring-data-commons'
        implementation 'org.springframework.security:spring-security-core'

        // Mapping
        implementation 'org.mapstruct:mapstruct:1.5.5.Final'
        annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

        // JSON processing
        implementation 'com.fasterxml.jackson.core:jackson-databind'
        implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
        
        // Saga orchestration
        implementation 'org.springframework.statemachine:spring-statemachine-core:4.0.0'
    }
}

configure(project(':order-events')) {
    dependencies {
        implementation project(':order-domain')

        // Spring Security for UserDetails
        implementation 'org.springframework.security:spring-security-core'

        // Kafka
        implementation 'org.springframework.kafka:spring-kafka'
        implementation 'org.apache.kafka:kafka-clients'

        // Avro serialization
        implementation 'io.confluent:kafka-avro-serializer:7.5.0'
        implementation 'org.apache.avro:avro:1.11.3'

        // Testing
        testImplementation 'org.springframework.kafka:spring-kafka-test'
        testImplementation 'org.testcontainers:kafka'
    }
}

configure(project(':order-web')) {
    apply plugin: 'org.springframework.boot'
    
    dependencies {
        implementation project(':order-domain')
        implementation project(':order-infrastructure')
        implementation project(':order-application')
        implementation project(':order-events')
        
        // Web
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.data:spring-data-commons'
        
        // Service Discovery
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
        
        // Monitoring
        implementation 'org.springframework.boot:spring-boot-starter-actuator'
        implementation 'io.micrometer:micrometer-registry-prometheus'
        implementation 'io.micrometer:micrometer-tracing-bridge-brave'
        implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
        
        // API Documentation
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
        
        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.security:spring-security-test'
        testImplementation 'org.springframework:spring-tx'
        testImplementation 'org.testcontainers:junit-jupiter'
    }
    
    bootJar {
        archiveFileName = 'order-management-service.jar'
        mainClass = 'com.nttdata.ndvn.order.web.OrderManagementApplication'
    }
    
    jar {
        enabled = false
    }
}

// Add Confluent repository for Kafka Avro serializer
allprojects {
    repositories {
        maven {
            url 'https://packages.confluent.io/maven/'
        }
    }
}
