# Schema Registry Configuration
app:
  events:
    schema-registry:
      enabled: true
      url: ${SCHEMA_REGISTRY_URL:http://localhost:8081}
      basic-auth-user-info: ${SCHEMA_REGISTRY_AUTH:}
      basic-auth-credentials-source: USER_INFO
      auto-register-schemas: true
      use-latest-version: true
      cache-capacity: 1000
      
      # Schema evolution policies
      evolution:
        compatibility-level: BACKWARD
        validate-schemas: true
        enable-schema-evolution: true
        
      # Subject naming strategy
      subject-naming:
        strategy: TopicNameStrategy
        # Alternative: RecordNameStrategy, TopicRecordNameStrategy
        
      # Schema validation rules
      validation:
        enforce-documentation: true
        enforce-naming-conventions: true
        enforce-event-fields: true
        max-schema-size: 1048576  # 1MB
        
# Kafka Configuration with Schema Registry
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      properties:
        schema.registry.url: ${app.events.schema-registry.url}
        auto.register.schemas: ${app.events.schema-registry.auto-register-schemas}
        use.latest.version: ${app.events.schema-registry.use-latest-version}
        
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      properties:
        schema.registry.url: ${app.events.schema-registry.url}
        specific.avro.reader: true
        auto.register.schemas: ${app.events.schema-registry.auto-register-schemas}
        use.latest.version: ${app.events.schema-registry.use-latest-version}

# Management endpoints for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,schema-registry
  endpoint:
    health:
      show-details: always
    schema-registry:
      enabled: true

# Logging configuration
logging:
  level:
    com.nttdata.ndvn.shared.events.schema: DEBUG
    io.confluent.kafka.schemaregistry: INFO
    org.apache.avro: INFO
    
# Metrics configuration
micrometer:
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      service: ${spring.application.name:unknown}
      environment: ${ENVIRONMENT:local}
