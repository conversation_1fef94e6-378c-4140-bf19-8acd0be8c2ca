{"type": "record", "name": "CustomerCreatedEvent", "namespace": "com.nttdata.ndvn.customer.events.avro", "doc": "Event published when a new customer is created in the customer management service", "fields": [{"name": "eventId", "type": "string", "doc": "Unique identifier for this event instance"}, {"name": "eventType", "type": "string", "default": "CustomerCreated", "doc": "Type of the event"}, {"name": "version", "type": "string", "default": "1.0", "doc": "Version of the event schema"}, {"name": "source", "type": "string", "default": "customer-management-service", "doc": "Source service that published this event"}, {"name": "timestamp", "type": "long", "logicalType": "timestamp-millis", "doc": "Timestamp when the event was created"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Correlation ID for tracking related events"}, {"name": "causationId", "type": ["null", "string"], "default": null, "doc": "ID of the event that caused this event"}, {"name": "aggregateId", "type": "string", "doc": "Customer ID (aggregate ID)"}, {"name": "aggregateType", "type": "string", "default": "Customer", "doc": "Type of the aggregate"}, {"name": "aggregateVersion", "type": "long", "default": 1, "doc": "Version of the customer aggregate"}, {"name": "customerId", "type": "string", "doc": "Unique identifier of the created customer"}, {"name": "customerNumber", "type": "string", "doc": "Business identifier for the customer"}, {"name": "customerType", "type": {"type": "enum", "name": "CustomerType", "symbols": ["INDIVIDUAL", "BUSINESS"]}, "doc": "Type of customer"}, {"name": "email", "type": "string", "doc": "Email address of the customer"}, {"name": "displayName", "type": "string", "doc": "Display name of the customer"}, {"name": "status", "type": {"type": "enum", "name": "CustomerStatus", "symbols": ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING"]}, "default": "PENDING", "doc": "Status of the customer"}, {"name": "classification", "type": ["null", "string"], "default": null, "doc": "Customer classification (e.g., STANDARD, PREMIUM, VIP)"}, {"name": "creditLimit", "type": ["null", {"type": "bytes", "logicalType": "decimal", "precision": 10, "scale": 2}], "default": null, "doc": "Credit limit for the customer"}, {"name": "segment", "type": ["null", "string"], "default": null, "doc": "Customer segment"}, {"name": "createdAt", "type": "long", "logicalType": "timestamp-millis", "doc": "Timestamp when the customer was created"}, {"name": "metadata", "type": {"type": "map", "values": "string"}, "default": {}, "doc": "Additional metadata"}]}