{"type": "record", "name": "UserCreatedEvent", "namespace": "com.nttdata.ndvn.user.events.avro", "doc": "Event published when a new user is created in the user management service", "fields": [{"name": "eventId", "type": "string", "doc": "Unique identifier for this event instance"}, {"name": "eventType", "type": "string", "default": "UserCreated", "doc": "Type of the event"}, {"name": "version", "type": "string", "default": "1.0", "doc": "Version of the event schema"}, {"name": "source", "type": "string", "default": "user-management-service", "doc": "Source service that published this event"}, {"name": "timestamp", "type": "long", "logicalType": "timestamp-millis", "doc": "Timestamp when the event was created"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Correlation ID for tracking related events"}, {"name": "causationId", "type": ["null", "string"], "default": null, "doc": "ID of the event that caused this event"}, {"name": "aggregateId", "type": "string", "doc": "User ID (aggregate ID)"}, {"name": "aggregateType", "type": "string", "default": "User", "doc": "Type of the aggregate"}, {"name": "aggregateVersion", "type": "long", "default": 1, "doc": "Version of the user aggregate"}, {"name": "userId", "type": "string", "doc": "Unique identifier of the created user"}, {"name": "username", "type": "string", "doc": "Username of the created user"}, {"name": "email", "type": "string", "doc": "Email address of the created user"}, {"name": "displayName", "type": ["null", "string"], "default": null, "doc": "Display name of the user"}, {"name": "enabled", "type": "boolean", "default": true, "doc": "Whether the user account is enabled"}, {"name": "emailVerified", "type": "boolean", "default": false, "doc": "Whether the user's email has been verified"}, {"name": "roles", "type": {"type": "array", "items": "string"}, "default": [], "doc": "List of roles assigned to the user"}, {"name": "createdAt", "type": "long", "logicalType": "timestamp-millis", "doc": "Timestamp when the user was created"}, {"name": "metadata", "type": {"type": "map", "values": "string"}, "default": {}, "doc": "Additional metadata"}]}