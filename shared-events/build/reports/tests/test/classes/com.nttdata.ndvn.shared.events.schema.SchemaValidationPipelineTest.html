<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - SchemaValidationPipelineTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>SchemaValidationPipelineTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.nttdata.ndvn.shared.events.schema.html">com.nttdata.ndvn.shared.events.schema</a> &gt; SchemaValidationPipelineTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">7</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.394s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
<li>
<a href="#tab2">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="shouldFailValidationForSchemaWithoutRequiredEventFields()"></a>
<h3 class="failures">shouldFailValidationForSchemaWithoutRequiredEventFields()</h3>
<span class="code">
<pre>java.lang.AssertionError: 
Expected size: 3 but was: 5 in:
[[ERROR] Compatibility validation failed: Cannot invoke &quot;io.confluent.kafka.schemaregistry.client.SchemaMetadata.getSchema()&quot; because the return value of &quot;io.confluent.kafka.schemaregistry.client.SchemaRegistryClient.getLatestSchemaMetadata(String)&quot; is null,
    [ERROR] Evolution rules validation failed: Cannot invoke &quot;io.confluent.kafka.schemaregistry.client.SchemaMetadata.getSchema()&quot; because the return value of &quot;io.confluent.kafka.schemaregistry.client.SchemaRegistryClient.getLatestSchemaMetadata(String)&quot; is null,
    [ERROR] Event schema must have 'eventId' field,
    [ERROR] Event schema must have 'eventType' field,
    [ERROR] Event schema must have 'timestamp' field]
	at com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldFailValidationForSchemaWithoutRequiredEventFields(SchemaValidationPipelineTest.java:123)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldGenerateDetailedReport()"></a>
<h3 class="failures">shouldGenerateDetailedReport()</h3>
<span class="code">
<pre>org.mockito.exceptions.misusing.UnnecessaryStubbingException: 
Unnecessary stubbings detected.
Clean &amp; maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -&gt; at com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldGenerateDetailedReport(SchemaValidationPipelineTest.java:314)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at app//org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:197)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldGenerateWarningsForMissingDocumentation()"></a>
<h3 class="failures">shouldGenerateWarningsForMissingDocumentation()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: 
Expecting value to be true but was false
	at app//com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldGenerateWarningsForMissingDocumentation(SchemaValidationPipelineTest.java:171)
	at java.base@21.0.7/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldHandleNullSchema()"></a>
<h3 class="failures">shouldHandleNullSchema()</h3>
<span class="code">
<pre>java.lang.AssertionError: 
Expected size: 1 but was: 6 in:
[[ERROR] Schema cannot be null,
    [ERROR] Compatibility validation failed: Cannot invoke &quot;io.confluent.kafka.schemaregistry.client.SchemaMetadata.getSchema()&quot; because the return value of &quot;io.confluent.kafka.schemaregistry.client.SchemaRegistryClient.getLatestSchemaMetadata(String)&quot; is null,
    [ERROR] Evolution rules validation failed: Cannot invoke &quot;io.confluent.kafka.schemaregistry.client.SchemaMetadata.getSchema()&quot; because the return value of &quot;io.confluent.kafka.schemaregistry.client.SchemaRegistryClient.getLatestSchemaMetadata(String)&quot; is null,
    [ERROR] Custom validation rule failed: Cannot invoke &quot;org.apache.avro.Schema.getField(String)&quot; because &quot;schema&quot; is null,
    [ERROR] Custom validation rule failed: Cannot invoke &quot;org.apache.avro.Schema.getName()&quot; because &quot;schema&quot; is null,
    [ERROR] Custom validation rule failed: Cannot invoke &quot;org.apache.avro.Schema.getDoc()&quot; because &quot;schema&quot; is null]
	at com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldHandleNullSchema(SchemaValidationPipelineTest.java:240)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldPassValidationForValidEventSchema()"></a>
<h3 class="failures">shouldPassValidationForValidEventSchema()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: 
Expecting value to be true but was false
	at app//com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldPassValidationForValidEventSchema(SchemaValidationPipelineTest.java:91)
	at java.base@21.0.7/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldValidateFieldTypes()"></a>
<h3 class="failures">shouldValidateFieldTypes()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: 
Expecting value to be true but was false
	at app//com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldValidateFieldTypes(SchemaValidationPipelineTest.java:286)
	at java.base@21.0.7/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
<div class="test">
<a name="shouldValidateNamingConventions()"></a>
<h3 class="failures">shouldValidateNamingConventions()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: 
Expecting value to be true but was false
	at app//com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.shouldValidateNamingConventions(SchemaValidationPipelineTest.java:218)
	at java.base@21.0.7/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base@21.0.7/java.util.ArrayList.forEach(ArrayList.java:1596)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">shouldFailValidationForSchemaWithoutRequiredEventFields()</td>
<td class="failures">0.049s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldGenerateDetailedReport()</td>
<td class="failures">0.022s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldGenerateWarningsForMissingDocumentation()</td>
<td class="failures">0.016s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldHandleNullSchema()</td>
<td class="failures">0.014s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldPassValidationForValidEventSchema()</td>
<td class="failures">0.005s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldValidateFieldTypes()</td>
<td class="failures">0.278s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">shouldValidateNamingConventions()</td>
<td class="failures">0.010s</td>
<td class="failures">failed</td>
</tr>
</table>
</div>
<div id="tab2" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>16:25:54.815 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.823 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 2 errors
16:25:54.839 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.839 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 4 errors
16:25:54.864 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.867 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 2 errors
16:25:54.874 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.876 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 6 errors
16:25:54.887 [Test worker] WARN org.apache.avro.Schema -- Ignored the com.nttdata.ndvn.user.events.UserCreatedEvent.timestamp.logicalType property (&quot;timestamp-millis&quot;). It should probably be nested inside the &quot;type&quot; for the field.
16:25:54.888 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: user.events-value
16:25:54.889 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: user.events-value with 2 errors
16:25:54.905 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.907 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 2 errors
16:25:54.952 [Test worker] INFO com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Starting schema validation for subject: test.events-value
16:25:54.956 [Test worker] WARN com.nttdata.ndvn.shared.events.schema.SchemaValidationPipeline -- Schema validation failed for subject: test.events-value with 5 errors
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Jun 28, 2025, 4:25:55 PM</p>
</div>
</div>
</body>
</html>
