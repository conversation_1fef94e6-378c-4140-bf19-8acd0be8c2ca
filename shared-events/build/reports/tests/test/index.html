<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">17</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">14</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.799s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">17%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Packages</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">BaseEventConsumerTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html#shouldHandleProcessingErrorWithRetry()">shouldHandleProcessingErrorWithRetry()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">BaseEventConsumerTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html#shouldHandleValidationErrorGracefully()">shouldHandleValidationErrorGracefully()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">BaseEventConsumerTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html#shouldProcessValidEventSuccessfully()">shouldProcessValidEventSuccessfully()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">BaseEventConsumerTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html#shouldSendNonRetryableErrorsToDlq()">shouldSendNonRetryableErrorsToDlq()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">BaseEventConsumerTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html#shouldSkipDuplicateEvents()">shouldSkipDuplicateEvents()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest.html">BaseEventPublisherTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest.html#shouldPublishEventSuccessfully()">shouldPublishEventSuccessfully()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest.html">BaseEventPublisherTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest.html#shouldValidateEventBeforePublishing()">shouldValidateEventBeforePublishing()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldFailValidationForSchemaWithoutRequiredEventFields()">shouldFailValidationForSchemaWithoutRequiredEventFields()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldGenerateDetailedReport()">shouldGenerateDetailedReport()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldGenerateWarningsForMissingDocumentation()">shouldGenerateWarningsForMissingDocumentation()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldHandleNullSchema()">shouldHandleNullSchema()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldPassValidationForValidEventSchema()">shouldPassValidationForValidEventSchema()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldValidateFieldTypes()">shouldValidateFieldTypes()</a>
</li>
<li>
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">SchemaValidationPipelineTest</a>.
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html#shouldValidateNamingConventions()">shouldValidateNamingConventions()</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="packages/com.nttdata.ndvn.shared.events.consumer.html">com.nttdata.ndvn.shared.events.consumer</a>
</td>
<td>6</td>
<td>5</td>
<td>0</td>
<td>1.017s</td>
<td class="failures">16%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.nttdata.ndvn.shared.events.publisher.html">com.nttdata.ndvn.shared.events.publisher</a>
</td>
<td>4</td>
<td>2</td>
<td>0</td>
<td>0.388s</td>
<td class="failures">50%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.nttdata.ndvn.shared.events.schema.html">com.nttdata.ndvn.shared.events.schema</a>
</td>
<td>7</td>
<td>7</td>
<td>0</td>
<td>0.394s</td>
<td class="failures">0%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="classes/com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest.html">com.nttdata.ndvn.shared.events.consumer.BaseEventConsumerTest</a>
</td>
<td>6</td>
<td>5</td>
<td>0</td>
<td>1.017s</td>
<td class="failures">16%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest.html">com.nttdata.ndvn.shared.events.publisher.BaseEventPublisherTest</a>
</td>
<td>4</td>
<td>2</td>
<td>0</td>
<td>0.388s</td>
<td class="failures">50%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest.html">com.nttdata.ndvn.shared.events.schema.SchemaValidationPipelineTest</a>
</td>
<td>7</td>
<td>7</td>
<td>0</td>
<td>0.394s</td>
<td class="failures">0%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Jun 28, 2025, 4:25:55 PM</p>
</div>
</div>
</body>
</html>
