package com.nttdata.ndvn.shared.events.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nttdata.ndvn.shared.events.BaseEvent;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * Unit tests for BaseEventConsumer.
 */
@ExtendWith(MockitoExtension.class)
class BaseEventConsumerTest {
    
    @Mock
    private Acknowledgment acknowledgment;
    
    private ObjectMapper objectMapper;
    private MeterRegistry meterRegistry;
    private TestEventConsumer eventConsumer;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        meterRegistry = new SimpleMeterRegistry();
        eventConsumer = new TestEventConsumer(objectMapper, meterRegistry);
    }
    
    @Test
    void shouldProcessValidEventSuccessfully() throws Exception {
        // Given
        TestEvent event = new TestEvent("TestEvent", "1.0", "test-service");
        event.setEventId(UUID.randomUUID());
        event.setTimestamp(Instant.now());
        
        String eventPayload = objectMapper.writeValueAsString(event);
        Map<String, Object> headers = createHeaders("test.topic");
        
        // When
        eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        
        // Then
        verify(acknowledgment).acknowledge();
        assertThat(eventConsumer.getProcessedEvents()).hasSize(1);
        assertThat(eventConsumer.getProcessedEvents().get(0).getEventId()).isEqualTo(event.getEventId());
    }
    
    @Test
    void shouldHandleDeserializationErrorGracefully() {
        // Given
        String invalidPayload = "{ invalid json }";
        Map<String, Object> headers = createHeaders("test.topic");
        
        // When
        eventConsumer.processTestEvent(invalidPayload, headers, acknowledgment);
        
        // Then
        verify(acknowledgment).acknowledge(); // Should acknowledge to prevent reprocessing
        assertThat(eventConsumer.getProcessedEvents()).isEmpty();
        assertThat(eventConsumer.getDlqEvents()).hasSize(1);
    }
    
    @Test
    void shouldHandleValidationErrorGracefully() throws Exception {
        // Given
        TestEvent event = new TestEvent("TestEvent", "1.0", "test-service");
        // Missing required fields (eventId and timestamp)
        
        String eventPayload = objectMapper.writeValueAsString(event);
        Map<String, Object> headers = createHeaders("test.topic");
        
        // When
        eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        
        // Then
        verify(acknowledgment).acknowledge(); // Should acknowledge to prevent reprocessing
        assertThat(eventConsumer.getProcessedEvents()).isEmpty();
        assertThat(eventConsumer.getDlqEvents()).hasSize(1);
    }
    
    @Test
    void shouldSkipDuplicateEvents() throws Exception {
        // Given
        TestEvent event = new TestEvent("TestEvent", "1.0", "test-service");
        event.setEventId(UUID.randomUUID());
        event.setTimestamp(Instant.now());
        
        String eventPayload = objectMapper.writeValueAsString(event);
        Map<String, Object> headers = createHeaders("test.topic");
        
        // Process the event first time
        eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        
        // When - process the same event again
        eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        
        // Then
        verify(acknowledgment, times(2)).acknowledge();
        assertThat(eventConsumer.getProcessedEvents()).hasSize(1); // Should only process once
    }
    
    @Test
    void shouldHandleProcessingErrorWithRetry() throws Exception {
        // Given
        TestEvent event = new TestEvent("TestEvent", "1.0", "test-service");
        event.setEventId(UUID.randomUUID());
        event.setTimestamp(Instant.now());
        
        String eventPayload = objectMapper.writeValueAsString(event);
        Map<String, Object> headers = createHeaders("test.topic");
        
        // Configure consumer to throw retryable error
        eventConsumer.setShouldThrowRetryableError(true);
        
        // When & Then
        try {
            eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        } catch (RuntimeException e) {
            assertThat(e.getMessage()).contains("Retryable error");
        }
        
        verify(acknowledgment, never()).acknowledge(); // Should not acknowledge for retryable errors
        assertThat(eventConsumer.getProcessedEvents()).isEmpty();
    }
    
    @Test
    void shouldSendNonRetryableErrorsToDlq() throws Exception {
        // Given
        TestEvent event = new TestEvent("TestEvent", "1.0", "test-service");
        event.setEventId(UUID.randomUUID());
        event.setTimestamp(Instant.now());
        
        String eventPayload = objectMapper.writeValueAsString(event);
        Map<String, Object> headers = createHeaders("test.topic");
        
        // Configure consumer to throw non-retryable error
        eventConsumer.setShouldThrowNonRetryableError(true);
        
        // When
        eventConsumer.processTestEvent(eventPayload, headers, acknowledgment);
        
        // Then
        verify(acknowledgment).acknowledge(); // Should acknowledge non-retryable errors
        assertThat(eventConsumer.getProcessedEvents()).isEmpty();
        assertThat(eventConsumer.getDlqEvents()).hasSize(1);
    }
    
    private Map<String, Object> createHeaders(String topic) {
        Map<String, Object> headers = new HashMap<>();
        headers.put("kafka_receivedTopic", topic);
        return headers;
    }
    
    /**
     * Test implementation of BaseEventConsumer.
     */
    private static class TestEventConsumer extends BaseEventConsumer {
        private final java.util.List<TestEvent> processedEvents = new java.util.ArrayList<>();
        private final java.util.List<String> dlqEvents = new java.util.ArrayList<>();
        private final java.util.Set<String> processedEventIds = new java.util.HashSet<>();
        private boolean shouldThrowRetryableError = false;
        private boolean shouldThrowNonRetryableError = false;
        
        public TestEventConsumer(ObjectMapper objectMapper, MeterRegistry meterRegistry) {
            super(objectMapper, meterRegistry);
        }
        
        public void processTestEvent(String eventPayload, Map<String, Object> headers, Acknowledgment acknowledgment) {
            processEvent(eventPayload, headers, acknowledgment, TestEvent.class, this::handleTestEvent);
        }
        
        private void handleTestEvent(TestEvent event) {
            if (shouldThrowRetryableError) {
                throw new RuntimeException("Connection failed");
            }
            
            if (shouldThrowNonRetryableError) {
                throw new IllegalArgumentException("Invalid event data");
            }
            
            processedEvents.add(event);
        }
        
        @Override
        protected String getConsumerGroup() {
            return "test-consumer-group";
        }
        
        @Override
        protected boolean isDuplicateEvent(BaseEvent event) {
            return processedEventIds.contains(event.getEventId().toString());
        }
        
        @Override
        protected void markEventAsProcessed(BaseEvent event) {
            processedEventIds.add(event.getEventId().toString());
        }
        
        @Override
        protected boolean isRetryableError(Exception e) {
            return e instanceof java.net.ConnectException;
        }
        
        @Override
        protected void sendToDeadLetterQueue(String eventPayload, String topic, String errorType, String errorMessage) {
            dlqEvents.add(eventPayload);
        }
        
        // Getters and setters for testing
        public java.util.List<TestEvent> getProcessedEvents() { return processedEvents; }
        public java.util.List<String> getDlqEvents() { return dlqEvents; }
        public void setShouldThrowRetryableError(boolean shouldThrowRetryableError) { 
            this.shouldThrowRetryableError = shouldThrowRetryableError; 
        }
        public void setShouldThrowNonRetryableError(boolean shouldThrowNonRetryableError) { 
            this.shouldThrowNonRetryableError = shouldThrowNonRetryableError; 
        }
    }
    
    /**
     * Test event class.
     */
    private static class TestEvent extends BaseEvent {
        public TestEvent() {
            super();
        }
        
        public TestEvent(String eventType, String version, String source) {
            super(eventType, version, source);
        }
    }
}
