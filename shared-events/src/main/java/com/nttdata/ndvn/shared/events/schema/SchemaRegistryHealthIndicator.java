package com.nttdata.ndvn.shared.events.schema;

import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Health indicator for Schema Registry connectivity and status.
 */
@Component
@ConditionalOnBean(SchemaManagementService.class)
@ConditionalOnClass(name = "org.springframework.boot.actuator.health.HealthIndicator")
public class SchemaRegistryHealthIndicator implements HealthIndicator {
    
    private final SchemaManagementService schemaManagementService;
    
    public SchemaRegistryHealthIndicator(SchemaManagementService schemaManagementService) {
        this.schemaManagementService = schemaManagementService;
    }
    
    @Override
    public Health health() {
        try {
            // Check if schema registry is accessible
            if (!schemaManagementService.isHealthy()) {
                return Health.down()
                    .withDetail("status", "Schema Registry is not accessible")
                    .build();
            }
            
            // Get basic metrics
            List<String> subjects = schemaManagementService.getAllSubjects();
            
            Health.Builder healthBuilder = Health.up()
                .withDetail("status", "Schema Registry is healthy")
                .withDetail("subjectCount", subjects.size());
            
            // Add subject details if not too many
            if (subjects.size() <= 10) {
                healthBuilder.withDetail("subjects", subjects);
            }
            
            return healthBuilder.build();
            
        } catch (Exception e) {
            return Health.down()
                .withDetail("status", "Schema Registry health check failed")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
