package com.nttdata.ndvn.shared.events.schema;

import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient;
import io.confluent.kafka.schemaregistry.client.rest.exceptions.RestClientException;
import org.apache.avro.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing Avro schemas in the Schema Registry.
 * 
 * This service provides functionality for:
 * - Schema registration and versioning
 * - Schema evolution validation
 * - Compatibility checking
 * - Schema retrieval and caching
 */
@Service
@ConditionalOnBean(SchemaRegistryClient.class)
public class SchemaManagementService {
    
    private static final Logger logger = LoggerFactory.getLogger(SchemaManagementService.class);
    
    private final SchemaRegistryClient schemaRegistryClient;
    private final SchemaRegistryConfig.SchemaEvolutionConfig evolutionConfig;
    
    public SchemaManagementService(SchemaRegistryClient schemaRegistryClient,
                                 SchemaRegistryConfig.SchemaEvolutionConfig evolutionConfig) {
        this.schemaRegistryClient = schemaRegistryClient;
        this.evolutionConfig = evolutionConfig;
    }
    
    /**
     * Registers a new schema or returns existing schema ID.
     * 
     * @param subject the schema subject name
     * @param schema the Avro schema
     * @return the schema ID
     */
    public int registerSchema(String subject, Schema schema) {
        try {
            logger.info("Registering schema for subject: {}", subject);
            
            // Check if schema already exists
            Optional<Integer> existingId = getSchemaId(subject, schema);
            if (existingId.isPresent()) {
                logger.info("Schema already exists for subject: {} with ID: {}", subject, existingId.get());
                return existingId.get();
            }
            
            // Validate schema evolution if enabled
            if (evolutionConfig.isEnableSchemaEvolution()) {
                validateSchemaEvolution(subject, schema);
            }
            
            // Register the schema
            int schemaId = schemaRegistryClient.register(subject, schema);
            logger.info("Successfully registered schema for subject: {} with ID: {}", subject, schemaId);
            
            return schemaId;
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to register schema for subject: {}", subject, e);
            throw new SchemaRegistrationException("Failed to register schema for subject: " + subject, e);
        }
    }
    
    /**
     * Gets the latest schema for a subject.
     * 
     * @param subject the schema subject name
     * @return the latest schema
     */
    public Optional<Schema> getLatestSchema(String subject) {
        try {
            Schema schema = schemaRegistryClient.getLatestSchemaMetadata(subject).getSchema();
            logger.debug("Retrieved latest schema for subject: {}", subject);
            return Optional.of(schema);
            
        } catch (IOException | RestClientException e) {
            logger.warn("Failed to retrieve latest schema for subject: {}", subject, e);
            return Optional.empty();
        }
    }
    
    /**
     * Gets a specific version of a schema.
     * 
     * @param subject the schema subject name
     * @param version the schema version
     * @return the schema
     */
    public Optional<Schema> getSchemaByVersion(String subject, int version) {
        try {
            Schema schema = schemaRegistryClient.getSchemaMetadata(subject, version).getSchema();
            logger.debug("Retrieved schema version {} for subject: {}", version, subject);
            return Optional.of(schema);
            
        } catch (IOException | RestClientException e) {
            logger.warn("Failed to retrieve schema version {} for subject: {}", version, subject, e);
            return Optional.empty();
        }
    }
    
    /**
     * Gets the schema ID for a specific schema.
     * 
     * @param subject the schema subject name
     * @param schema the schema
     * @return the schema ID if exists
     */
    public Optional<Integer> getSchemaId(String subject, Schema schema) {
        try {
            int schemaId = schemaRegistryClient.getId(subject, schema);
            return Optional.of(schemaId);
            
        } catch (IOException | RestClientException e) {
            logger.debug("Schema not found for subject: {}", subject);
            return Optional.empty();
        }
    }
    
    /**
     * Gets all versions of a schema.
     * 
     * @param subject the schema subject name
     * @return list of schema versions
     */
    public List<Integer> getSchemaVersions(String subject) {
        try {
            List<Integer> versions = schemaRegistryClient.getAllVersions(subject);
            logger.debug("Retrieved {} versions for subject: {}", versions.size(), subject);
            return versions;
            
        } catch (IOException | RestClientException e) {
            logger.warn("Failed to retrieve versions for subject: {}", subject, e);
            return List.of();
        }
    }
    
    /**
     * Validates schema evolution compatibility.
     * 
     * @param subject the schema subject name
     * @param newSchema the new schema to validate
     * @return true if compatible, false otherwise
     */
    public boolean validateSchemaEvolution(String subject, Schema newSchema) {
        try {
            // Get the latest schema for comparison
            Optional<Schema> latestSchema = getLatestSchema(subject);
            if (latestSchema.isEmpty()) {
                logger.info("No existing schema found for subject: {}, allowing registration", subject);
                return true;
            }
            
            // Test compatibility
            boolean isCompatible = schemaRegistryClient.testCompatibility(subject, newSchema);
            
            if (isCompatible) {
                logger.info("Schema evolution validation passed for subject: {}", subject);
            } else {
                logger.error("Schema evolution validation failed for subject: {} - incompatible with existing schema", subject);
                throw new SchemaEvolutionException("Schema is not compatible with existing schema for subject: " + subject);
            }
            
            return isCompatible;
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to validate schema evolution for subject: {}", subject, e);
            throw new SchemaEvolutionException("Failed to validate schema evolution for subject: " + subject, e);
        }
    }
    
    /**
     * Sets the compatibility level for a subject.
     * 
     * @param subject the schema subject name
     * @param compatibilityLevel the compatibility level
     */
    public void setCompatibilityLevel(String subject, SchemaRegistryConfig.CompatibilityLevel compatibilityLevel) {
        try {
            schemaRegistryClient.updateCompatibility(subject, compatibilityLevel.getValue());
            logger.info("Set compatibility level {} for subject: {}", compatibilityLevel, subject);
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to set compatibility level for subject: {}", subject, e);
            throw new SchemaManagementException("Failed to set compatibility level for subject: " + subject, e);
        }
    }
    
    /**
     * Gets the compatibility level for a subject.
     * 
     * @param subject the schema subject name
     * @return the compatibility level
     */
    public Optional<String> getCompatibilityLevel(String subject) {
        try {
            String compatibility = schemaRegistryClient.getCompatibility(subject);
            logger.debug("Retrieved compatibility level {} for subject: {}", compatibility, subject);
            return Optional.of(compatibility);
            
        } catch (IOException | RestClientException e) {
            logger.warn("Failed to retrieve compatibility level for subject: {}", subject, e);
            return Optional.empty();
        }
    }
    
    /**
     * Deletes a schema subject.
     * 
     * @param subject the schema subject name
     * @return list of deleted versions
     */
    public List<Integer> deleteSubject(String subject) {
        try {
            List<Integer> deletedVersions = schemaRegistryClient.deleteSubject(subject);
            logger.info("Deleted subject: {} with versions: {}", subject, deletedVersions);
            return deletedVersions;
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to delete subject: {}", subject, e);
            throw new SchemaManagementException("Failed to delete subject: " + subject, e);
        }
    }
    
    /**
     * Deletes a specific version of a schema.
     * 
     * @param subject the schema subject name
     * @param version the version to delete
     * @return the deleted version number
     */
    public int deleteSchemaVersion(String subject, int version) {
        try {
            int deletedVersion = schemaRegistryClient.deleteSchemaVersion(subject, String.valueOf(version));
            logger.info("Deleted version {} for subject: {}", deletedVersion, subject);
            return deletedVersion;
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to delete version {} for subject: {}", version, subject, e);
            throw new SchemaManagementException("Failed to delete version " + version + " for subject: " + subject, e);
        }
    }
    
    /**
     * Gets all subjects in the schema registry.
     * 
     * @return list of subject names
     */
    public List<String> getAllSubjects() {
        try {
            List<String> subjects = schemaRegistryClient.getAllSubjects();
            logger.debug("Retrieved {} subjects from schema registry", subjects.size());
            return subjects;
            
        } catch (IOException | RestClientException e) {
            logger.error("Failed to retrieve subjects from schema registry", e);
            throw new SchemaManagementException("Failed to retrieve subjects from schema registry", e);
        }
    }
    
    /**
     * Checks if the schema registry is healthy.
     * 
     * @return true if healthy, false otherwise
     */
    public boolean isHealthy() {
        try {
            // Try to get all subjects as a health check
            schemaRegistryClient.getAllSubjects();
            return true;
            
        } catch (Exception e) {
            logger.warn("Schema registry health check failed", e);
            return false;
        }
    }
    
    /**
     * Exception thrown when schema registration fails.
     */
    public static class SchemaRegistrationException extends RuntimeException {
        public SchemaRegistrationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * Exception thrown when schema evolution validation fails.
     */
    public static class SchemaEvolutionException extends RuntimeException {
        public SchemaEvolutionException(String message) {
            super(message);
        }
        
        public SchemaEvolutionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * Exception thrown when schema management operations fail.
     */
    public static class SchemaManagementException extends RuntimeException {
        public SchemaManagementException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
