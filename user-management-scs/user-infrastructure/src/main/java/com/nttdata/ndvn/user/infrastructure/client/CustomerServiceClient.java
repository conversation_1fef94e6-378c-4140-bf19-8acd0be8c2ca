package com.nttdata.ndvn.user.infrastructure.client;

import com.nttdata.ndvn.shared.infrastructure.client.BaseApiClient;
import com.nttdata.ndvn.shared.infrastructure.discovery.ServiceDiscoveryClient;
import com.nttdata.ndvn.shared.infrastructure.security.JwtTokenProvider;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * API client for Customer Management Service integration from User Management.
 * 
 * Provides methods for:
 * - Customer profile synchronization
 * - Customer status updates
 * - Customer validation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class CustomerServiceClient extends BaseApiClient {
    
    private static final String SERVICE_NAME = "customer-management-service";
    
    public CustomerServiceClient(ServiceDiscoveryClient serviceDiscovery,
                               WebClient.Builder webClientBuilder,
                               JwtTokenProvider tokenProvider,
                               MeterRegistry meterRegistry) {
        super(SERVICE_NAME, serviceDiscovery, webClientBuilder, tokenProvider, meterRegistry);
    }
    
    @Override
    protected String getClientServiceName() {
        return "user-management-service";
    }
    
    /**
     * Get customer profile by user ID.
     * 
     * @param userId the user ID
     * @return CompletableFuture with customer profile
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getCustomerByUserIdFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Object> getCustomerByUserId(UUID userId) {
        return get("/api/v1/customers/user/{userId}", Object.class, userId);
    }
    
    /**
     * Update customer status when user status changes.
     * 
     * @param userId the user ID
     * @param status the new status
     * @return CompletableFuture with update result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "updateCustomerStatusFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Object> updateCustomerStatus(UUID userId, String status) {
        Object request = new Object() {
            public final String status = CustomerServiceClient.this.getStatus(status);
        };
        return put("/api/v1/customers/user/{userId}/status", request, Object.class, userId);
    }
    
    /**
     * Validate if user has associated customer profile.
     * 
     * @param userId the user ID
     * @return CompletableFuture with validation result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "validateCustomerExistsFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Boolean> validateCustomerExists(UUID userId) {
        return get("/api/v1/customers/user/{userId}/exists", Boolean.class, userId);
    }
    
    /**
     * Sync customer profile with user profile changes.
     * 
     * @param userId the user ID
     * @param profileData the updated profile data
     * @return CompletableFuture with sync result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "syncCustomerProfileFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Object> syncCustomerProfile(UUID userId, Object profileData) {
        return put("/api/v1/customers/user/{userId}/sync", profileData, Object.class, userId);
    }
    
    // Fallback methods
    
    public CompletableFuture<Object> getCustomerByUserIdFallback(UUID userId, Exception ex) {
        logger.warn("Fallback: Failed to get customer for user {}", userId, ex);
        return CompletableFuture.completedFuture(null);
    }
    
    public CompletableFuture<Object> updateCustomerStatusFallback(UUID userId, String status, Exception ex) {
        logger.warn("Fallback: Failed to update customer status for user {}", userId, ex);
        return CompletableFuture.completedFuture(createStatusUpdateResult(false));
    }
    
    public CompletableFuture<Boolean> validateCustomerExistsFallback(UUID userId, Exception ex) {
        logger.warn("Fallback: Failed to validate customer exists for user {}", userId, ex);
        return CompletableFuture.completedFuture(false);
    }
    
    public CompletableFuture<Object> syncCustomerProfileFallback(UUID userId, Object profileData, Exception ex) {
        logger.warn("Fallback: Failed to sync customer profile for user {}", userId, ex);
        return CompletableFuture.completedFuture(createSyncResult(false));
    }
    
    private String getStatus(String status) {
        return status;
    }
    
    private Object createStatusUpdateResult(boolean success) {
        return new Object() {
            public final boolean updated = success;
            public final String message = success ? "Status updated" : "Status update failed";
        };
    }
    
    private Object createSyncResult(boolean success) {
        return new Object() {
            public final boolean synced = success;
            public final String message = success ? "Profile synced" : "Profile sync failed";
        };
    }
}
