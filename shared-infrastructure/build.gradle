plugins {
    id 'java-library'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

apply plugin: 'io.spring.dependency-management'

group = 'com.nttdata.ndvn.shared'
version = '1.0.0-SNAPSHOT'

description = 'Shared Infrastructure Framework - Common service-to-service communication infrastructure'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven { url 'https://packages.confluent.io/maven/' }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.1"
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2024.0.0"
        mavenBom "org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE"
    }
}

dependencies {
    // Spring Boot Core
    api 'org.springframework.boot:spring-boot-starter'
    api 'org.springframework.boot:spring-boot-starter-validation'
    api 'org.springframework.boot:spring-boot-starter-webflux'
    
    // Spring Cloud
    api 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    api 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
    
    // Resilience4j
    api 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j'
    api 'io.github.resilience4j:resilience4j-spring-boot3:2.2.0'
    api 'io.github.resilience4j:resilience4j-reactor:2.2.0'
    
    // Security
    api 'org.springframework.boot:spring-boot-starter-security'
    api 'org.springframework.security:spring-security-oauth2-jose'
    api 'org.springframework.security:spring-security-oauth2-client'
    
    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Metrics and Monitoring
    api 'io.micrometer:micrometer-core'
    api 'io.micrometer:micrometer-registry-prometheus'
    api 'io.micrometer:micrometer-tracing-bridge-brave'
    
    // Validation
    api 'jakarta.validation:jakarta.validation-api'
    
    // Logging
    api 'org.slf4j:slf4j-api'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.cloud:spring-cloud-contract-wiremock'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:consul'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:kafka'
    testImplementation 'io.github.resilience4j:resilience4j-test:2.2.0'

    // Contract Testing
    testImplementation 'au.com.dius.pact.consumer:junit5:4.6.2'
    testImplementation 'au.com.dius.pact.provider:junit5:4.6.2'

    // Chaos Engineering
    testImplementation 'org.springframework.cloud:spring-cloud-starter-contract-stub-runner'
    testImplementation 'com.github.tomakehurst:wiremock-jre8:2.35.0'
}

test {
    useJUnitPlatform()
}

jar {
    enabled = true
    archiveClassifier = ''
}
